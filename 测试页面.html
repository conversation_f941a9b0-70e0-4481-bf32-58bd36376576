<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>弹窗测试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background: #f5f5f5;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 5px;
      cursor: pointer;
      margin: 10px;
      font-size: 16px;
    }
    
    .test-btn:hover {
      background: #0056b3;
    }
    
    /* 模拟目标弹窗样式 */
    .test-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: none;
      z-index: 1000;
      align-items: center;
      justify-content: center;
    }
    
    .modal-content {
      background: white;
      padding: 30px;
      border-radius: 10px;
      max-width: 500px;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    
    .MDL_headerCloseIcon_5-118-0 {
      position: absolute;
      top: 15px;
      right: 15px;
      cursor: pointer;
      width: 24px;
      height: 24px;
    }
    
    .ICN_outerWrapper_5-118-0 {
      width: 100%;
      height: 100%;
    }
    
    .ICN_svgIcon_5-118-0 {
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>弹窗杀手测试页面</h1>
    <p>这个页面用于测试弹窗杀手插件的功能。</p>
    
    <h2>测试功能</h2>
    <button class="test-btn" onclick="showTestModal()">显示目标弹窗</button>
    <button class="test-btn" onclick="showExcludedModal()">显示排除的弹窗</button>
    <button class="test-btn" onclick="showMultipleModals()">显示多个弹窗</button>
    <button class="test-btn" onclick="hideAllModals()">手动关闭所有弹窗</button>
    
    <h2>说明</h2>
    <ul>
      <li>点击"显示目标弹窗"会显示一个包含特定关闭按钮的弹窗（会被自动关闭）</li>
      <li>点击"显示排除的弹窗"会显示一个包含MDL_iconWrapper的弹窗（不会被自动关闭）</li>
      <li>如果插件正常工作，只有目标弹窗会在0.5秒内自动关闭</li>
      <li>插件只会关闭特定的SVG元素，排除MDL_iconWrapper容器内的按钮</li>
      <li>计数器会显示已关闭的弹窗数量</li>
    </ul>
    
    <h2>插件状态</h2>
    <p>请打开插件面板查看当前状态和计数器。</p>
  </div>
  
  <!-- 目标测试弹窗（会被自动关闭） -->
  <div id="testModal" class="test-modal" role="dialog">
    <div class="modal-content">
      <svg xmlns="http://www.w3.org/2000/svg"
           viewBox="0 0 1024 1024"
           data-testid="beast-core-modal-icon-close"
           class="ICN_outerWrapper_5-118-0 MDL_headerCloseIcon_5-118-0 ICN_svgIcon_5-118-0">
        <path d="M566.306 512l244.376-244.376c14.997-14.996 14.997-39.309 0-54.305-14.996-14.997-39.309-14.997-54.305 0L512 457.694 267.624 213.318c-14.996-14.997-39.31-14.997-54.306 0-14.996 14.996-14.996 39.309 0 54.305L457.694 512 213.318 756.376c-14.996 14.996-14.996 39.31 0 54.306 14.996 14.996 39.31 14.996 54.306 0L512 566.306l244.376 244.376c14.996 14.996 39.309 14.996 54.305 0 14.997-14.996 14.997-39.31 0-54.306L566.306 512z"></path>
      </svg>

      <h3>目标弹窗</h3>
      <p>这是一个包含目标关闭按钮的测试弹窗。</p>
      <p style="color: green;">✅ 如果插件正常工作，这个弹窗会自动关闭。</p>
    </div>
  </div>

  <!-- 排除测试弹窗（不会被自动关闭） -->
  <div id="excludedModal" class="test-modal" role="dialog">
    <div class="modal-content">
      <div class="MDL_iconWrapper_5-118-0">
        <svg xmlns="http://www.w3.org/2000/svg"
             viewBox="0 0 1024 1024"
             data-testid="beast-core-modal-icon-close"
             class="ICN_outerWrapper_5-118-0 MDL_headerCloseIcon_5-118-0 ICN_svgIcon_5-118-0">
          <path d="M566.306 512l244.376-244.376c14.997-14.996 14.997-39.309 0-54.305-14.996-14.997-39.309-14.997-54.305 0L512 457.694 267.624 213.318c-14.996-14.997-39.31-14.997-54.306 0-14.996 14.996-14.996 39.309 0 54.305L457.694 512 213.318 756.376c-14.996 14.996-14.996 39.31 0 54.306 14.996 14.996 39.31 14.996 54.306 0L512 566.306l244.376 244.376c14.996 14.996 39.309 14.996 54.305 0 14.997-14.996 14.997-39.31 0-54.306L566.306 512z"></path>
        </svg>
      </div>

      <h3>排除弹窗</h3>
      <p>这是一个包含MDL_iconWrapper容器的测试弹窗。</p>
      <p style="color: red;">❌ 这个弹窗不会被自动关闭（已排除）。</p>
    </div>
  </div>
  
  <script>
    let modalCount = 0;
    
    function showTestModal() {
      document.getElementById('testModal').style.display = 'flex';
      console.log('显示目标测试弹窗');
    }

    function showExcludedModal() {
      document.getElementById('excludedModal').style.display = 'flex';
      console.log('显示排除的测试弹窗');
    }

    function showMultipleModals() {
      showTestModal();
      setTimeout(() => {
        showExcludedModal();
      }, 1000);
    }

    function hideAllModals() {
      document.getElementById('testModal').style.display = 'none';
      document.getElementById('excludedModal').style.display = 'none';
      console.log('手动关闭所有弹窗');
    }
    
    // 监听弹窗关闭事件
    document.addEventListener('click', function(e) {
      if (e.target.closest('svg[data-testid="beast-core-modal-icon-close"]')) {
        console.log('检测到关闭按钮点击');
        hideAllModals();
      }

      // 点击弹窗外部关闭
      if (e.target.classList.contains('test-modal')) {
        hideAllModals();
      }
    });
  </script>
</body>
</html>
